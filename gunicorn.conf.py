import multiprocessing
import os

# 绑定地址和端口
bind = "0.0.0.0:7031"

# 工作进程数 (Docker 中建议适当减少)
workers = min(multiprocessing.cpu_count() * 2 + 1, 4)

# 使用 gevent
worker_class = "gevent"

# 每个工作进程的最大并发请求数
worker_connections = 100

# 超时设置 (比 Docker 默认超时短)
timeout = 120

# 保持连接
keepalive = 20

# 日志配置 (直接输出到 stdout/stderr 方便 Docker 采集)
loglevel = "info"
accesslog = "-"
errorlog = "-"

# 关闭 preload_app (与自动重启更兼容)
preload_app = False

# 自动重启配置
max_requests = 1000  # 处理1000个请求后重启worker
max_requests_jitter = 50  # 随机抖动防止所有worker同时重启

# 关键配置：当worker异常退出时，让master也退出
graceful_timeout = 30
reload = True  # 开发时使用，生产环境应该设为False
