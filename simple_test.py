#!/usr/bin/env python3
"""
简单测试脚本
"""

import requests
import sys

def test_pages():
    """测试各个页面是否正常加载"""
    base_url = "http://127.0.0.1:5000"
    
    pages = [
        ("首页", "/"),
        ("实验原理", "/theory"),
        ("实验步骤", "/steps"),
        ("数据输入1", "/input/1"),
        ("数据输入2", "/input/2"),
    ]
    
    print("🧪 测试页面加载...")
    
    for name, path in pages:
        try:
            response = requests.get(f"{base_url}{path}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: 正常")
            else:
                print(f"❌ {name}: 错误 {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: 异常 {e}")
    
    print("\n🎉 测试完成！")
    print(f"📱 请访问: {base_url}")

if __name__ == "__main__":
    test_pages()
