from flask import Flask, render_template, request, session, redirect, url_for
import numpy as np
from scipy import stats
import matplotlib.pyplot as plt
import io
import base64
from matplotlib.figure import Figure
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-for-session'  # 用于session加密

@app.route('/')
def index():
    """首页 - 实验介绍和原理说明"""
    return render_template('index.html')

@app.route('/theory')
def theory():
    """实验原理页面"""
    return render_template('theory.html')

@app.route('/steps')
def steps():
    """实验步骤页面"""
    return render_template('steps.html')

@app.route('/input/<int:step>')
def input_data(step):
    """数据输入页面 - 分步骤输入"""
    if step < 1 or step > 5:
        return redirect(url_for('index'))

    # 定义常量
    currents = [0.55, 0.60, 0.65, 0.70, 0.75]  # 安培
    temperatures = [1800, 1880, 1960, 2040, 2120]  # 开尔文
    voltages = [16, 25, 36, 49, 64, 81, 100, 121]  # 伏特

    current_temp_index = step - 1
    current_temp = temperatures[current_temp_index]
    current_current = currents[current_temp_index]

    return render_template('input.html',
                         step=step,
                         current_temp=current_temp,
                         current_current=current_current,
                         voltages=voltages,
                         total_steps=5)

@app.route('/save_data/<int:step>', methods=['POST'])
def save_data(step):
    """保存当前步骤的数据"""
    if step < 1 or step > 5:
        return redirect(url_for('index'))

    # 初始化session数据
    if 'experiment_data' not in session:
        session['experiment_data'] = {}

    # 保存当前步骤的数据
    voltages = [16, 25, 36, 49, 64, 81, 100, 121]
    step_data = []

    for i, voltage in enumerate(voltages):
        value = request.form.get(f'current_{i}', '')
        if value.strip():
            try:
                step_data.append(float(value))
            except ValueError:
                # 如果输入无效，生成模拟数据
                step_data.append(generate_simulated_current(step-1, i))
        else:
            # 如果没有输入，生成模拟数据
            step_data.append(generate_simulated_current(step-1, i))

    session['experiment_data'][f'step_{step}'] = step_data
    session.modified = True

    # 如果是最后一步，跳转到结果页面
    if step == 5:
        return redirect(url_for('results'))
    else:
        # 否则跳转到下一步
        return redirect(url_for('input_data', step=step+1))

def generate_simulated_current(temp_index, voltage_index):
    """生成模拟电流数据"""
    temperatures = [1800, 1880, 1960, 2040, 2120]
    voltages = [16, 25, 36, 49, 64, 81, 100, 121]

    k = 8.617e-5  # 玻尔兹曼常数，eV/K
    work_function = 4.5  # 假设的逸出功，eV
    A = 1.2e6  # Richardson常数
    temp = temperatures[temp_index]
    voltage = voltages[voltage_index]

    base_current = A * (temp ** 2) * np.exp(-work_function / (k * temp))
    schottky_factor = np.exp(0.04 * np.sqrt(voltage) / (k * temp))
    simulated_current = base_current * schottky_factor * (1 + 0.05 * np.random.randn())
    return simulated_current

@app.route('/results')
def results():
    """结果展示页面"""
    if 'experiment_data' not in session:
        return redirect(url_for('index'))

    # 重构数据格式
    measured_currents = []
    for i in range(1, 6):
        step_key = f'step_{i}'
        if step_key in session['experiment_data']:
            measured_currents.append(session['experiment_data'][step_key])
        else:
            # 如果某步数据缺失，生成模拟数据
            step_data = [generate_simulated_current(i-1, j) for j in range(8)]
            measured_currents.append(step_data)

    # 定义常量
    temperatures = [1800, 1880, 1960, 2040, 2120]
    voltages = [16, 25, 36, 49, 64, 81, 100, 121]

    # 数据处理
    results = process_data(temperatures, voltages, measured_currents)
    return render_template('results.html', results=results)

@app.route('/reset')
def reset():
    """重置实验数据"""
    session.pop('experiment_data', None)
    return redirect(url_for('index'))


def process_data(temperatures, voltages, measured_currents):
    # 计算相关参数
    results = {}
    results['temperatures'] = temperatures
    results['voltages'] = voltages
    results['currents'] = measured_currents
    results['temp_count'] = len(temperatures)
    results['voltage_count'] = len(voltages)

    # 1. 绘制I-U特性曲线
    fig = Figure(figsize=(10, 6))
    ax = fig.add_subplot(111)

    for i, temp in enumerate(temperatures):
        ax.plot(voltages, measured_currents[i], 'o-', label=f'T={temp}K')

    ax.set_xlabel('阳极电压 U (V)')
    ax.set_ylabel('电流 I (A)')
    ax.set_title('I-U特性曲线')
    ax.legend()
    ax.grid(True)

    buf = io.BytesIO()
    fig.savefig(buf, format='png')
    buf.seek(0)
    results['iu_plot'] = base64.b64encode(buf.getvalue()).decode('utf-8')

    # 2. logI与sqrt(U)的线性拟合 (为每个温度做拟合)
    fig = Figure(figsize=(10, 6))
    ax = fig.add_subplot(111)

    sqrt_voltages = np.sqrt(voltages)
    logu_fits = []

    for i, temp in enumerate(temperatures):
        currents = measured_currents[i]
        log_currents = np.log10(currents)

        # 线性拟合
        slope, intercept, r_value, _, _ = stats.linregress(sqrt_voltages, log_currents)

        # 绘制数据点和拟合线
        ax.plot(sqrt_voltages, log_currents, 'o', label=f'T={temp}K (数据)')
        ax.plot(sqrt_voltages, slope * sqrt_voltages + intercept, '-',
                label=f'T={temp}K (拟合: y={slope:.4f}x+{intercept:.4f})')

        logu_fits.append({
            'temp': f"{temp}",
            'slope': f"{slope:.4f}",
            'intercept': f"{intercept:.4f}",
            'r_squared': f"{r_value ** 2:.4f}"
        })

    ax.set_xlabel('sqrt(U)')
    ax.set_ylabel('log(I)')
    ax.set_title('log(I)与sqrt(U)的线性关系')
    ax.legend()
    ax.grid(True)

    buf = io.BytesIO()
    fig.savefig(buf, format='png')
    buf.seek(0)
    results['logu_plot'] = base64.b64encode(buf.getvalue()).decode('utf-8')
    results['logu_fits'] = logu_fits

    # 3. lg(I/T²)与1/T的线性拟合
    fig = Figure(figsize=(10, 6))
    ax = fig.add_subplot(111)

    # 选择一个固定的电压进行分析，这里选择中间值
    mid_index = len(voltages) // 2
    voltage_index = mid_index
    fixed_voltage = voltages[voltage_index]

    # 准备数据
    log_i_t2_values = []
    inv_temp_values = []
    temp_data = []

    for i, temp in enumerate(temperatures):
        current = measured_currents[i][voltage_index]  # 使用固定电压下的电流
        log_i_t2 = np.log10(current / (temp ** 2))
        inv_temp = 1 / temp

        log_i_t2_values.append(log_i_t2)
        inv_temp_values.append(inv_temp)

        temp_data.append({
            'temp': f"{temp}",
            'inv_temp': f"{inv_temp:.7f}",
            'current': f"{current:.6e}",
            'log_i_t2': f"{log_i_t2:.6f}"
        })

    # 线性拟合
    slope2, intercept2, r_value2, _, _ = stats.linregress(inv_temp_values, log_i_t2_values)

    # 绘制数据点和拟合线
    ax.plot(inv_temp_values, log_i_t2_values, 'o', label='实验数据')
    ax.plot(inv_temp_values, slope2 * np.array(inv_temp_values) + intercept2, 'r-',
            label=f'拟合线: y = {slope2:.2f}x + {intercept2:.2f}')

    ax.set_xlabel('1/T (K⁻¹)')
    ax.set_ylabel('lg(I/T²)')
    ax.set_title(f'lg(I/T²)与1/T的线性关系 (U={fixed_voltage}V)')
    ax.legend()
    ax.grid(True)

    buf = io.BytesIO()
    fig.savefig(buf, format='png')
    buf.seek(0)
    results['temp_plot'] = base64.b64encode(buf.getvalue()).decode('utf-8')

    results['slope2'] = f"{slope2:.4f}"
    results['intercept2'] = f"{intercept2:.4f}"
    results['r_squared2'] = f"{r_value2 ** 2:.4f}"

    # 计算电子逸出功
    # 根据Richardson-Dushman方程: j = AT²exp(-φ/kT)
    # 取对数: lg(j/T²) = lg(A) - φ/(k·T·ln(10))
    # 斜率 = -φ/(k·ln(10))
    k = 8.617e-5  # 玻尔兹曼常数，eV/K
    ln10 = np.log(10)
    work_function = -slope2 * k * ln10
    results['work_function'] = f"{work_function:.4f}"

    results['temp_data'] = temp_data

    return results


if __name__ == '__main__':
    app.run(debug=True)

