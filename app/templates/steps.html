<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实验步骤 - 金属电子逸出功实验</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 2rem auto;
            padding: 0;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        .content-section {
            padding: 3rem 2rem;
        }
        .step-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .step-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        .warning-box {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 5px solid #ff6b6b;
        }
        .tip-box {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 5px solid #00d2d3;
        }
        .equipment-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            margin: 2rem 0;
        }
        .btn-custom {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.6);
            color: white;
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
            color: #6c757d;
        }
        .step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        .parameter-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="fas fa-atom me-2"></i>电子逸出功实验
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">首页</a>
                <a class="nav-link" href="{{ url_for('theory') }}">实验原理</a>
                <a class="nav-link active" href="{{ url_for('steps') }}">实验步骤</a>
                <a class="nav-link" href="{{ url_for('input_data', step=1) }}">开始实验</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid" style="margin-top: 76px;">
        <div class="main-container">
            <!-- Hero Section -->
            <div class="hero-section">
                <h1><i class="fas fa-list-ol me-3"></i>实验步骤</h1>
                <p class="lead">详细的实验操作指南与注意事项</p>
                <div class="step-indicator">
                    <div class="step">1</div>
                    <div class="step">2</div>
                    <div class="step active">3</div>
                    <div class="step">4</div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <div class="row">
                    <div class="col-lg-10 mx-auto">
                        
                        <!-- 实验设备 -->
                        <div class="equipment-list">
                            <h3><i class="fas fa-tools me-2"></i>实验设备</h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul>
                                        <li>热电子发射管（二极管）</li>
                                        <li>直流稳压电源（灯丝电源）</li>
                                        <li>直流稳压电源（阳极电源）</li>
                                        <li>数字万用表（电流表）</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul>
                                        <li>数字万用表（电压表）</li>
                                        <li>温度计或红外测温仪</li>
                                        <li>连接导线</li>
                                        <li>实验记录表</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 实验参数 -->
                        <div class="step-card">
                            <h3><i class="fas fa-cog me-2"></i>实验参数设置</h3>
                            <div class="parameter-table">
                                <table class="table table-striped mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>参数</th>
                                            <th>数值</th>
                                            <th>说明</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>灯丝电流</td>
                                            <td>0.55, 0.60, 0.65, 0.70, 0.75 A</td>
                                            <td>对应不同的灯丝温度</td>
                                        </tr>
                                        <tr>
                                            <td>灯丝温度</td>
                                            <td>1800, 1880, 1960, 2040, 2120 K</td>
                                            <td>通过电流控制温度</td>
                                        </tr>
                                        <tr>
                                            <td>阳极电压</td>
                                            <td>16, 25, 36, 49, 64, 81, 100, 121 V</td>
                                            <td>8个不同的电压值</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 实验步骤 -->
                        <div class="step-card">
                            <div class="step-number">1</div>
                            <h4>实验准备</h4>
                            <ul>
                                <li>检查实验设备是否完好，确保所有连接线正常</li>
                                <li>按照电路图正确连接实验装置</li>
                                <li>预热设备，确保电源稳定</li>
                                <li>准备实验记录表格</li>
                            </ul>
                            <div class="warning-box">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>安全注意事项</h6>
                                <p class="mb-0">实验涉及高温和高电压，操作时要特别小心。确保设备接地良好，避免触电危险。</p>
                            </div>
                        </div>

                        <div class="step-card">
                            <div class="step-number">2</div>
                            <h4>设置灯丝电流</h4>
                            <ul>
                                <li>将灯丝电流调节到0.55A，等待温度稳定（约5-10分钟）</li>
                                <li>使用温度计或红外测温仪测量灯丝温度，确认为1800K左右</li>
                                <li>记录实际的灯丝电流和温度值</li>
                            </ul>
                            <div class="tip-box">
                                <h6><i class="fas fa-lightbulb me-2"></i>实验技巧</h6>
                                <p class="mb-0">温度稳定是关键，建议每次调节电流后等待充分时间，确保读数稳定。</p>
                            </div>
                        </div>

                        <div class="step-card">
                            <div class="step-number">3</div>
                            <h4>测量I-U特性</h4>
                            <ul>
                                <li>保持灯丝电流不变，逐步调节阳极电压</li>
                                <li>从16V开始，按照25V、36V、49V、64V、81V、100V、121V的顺序测量</li>
                                <li>每个电压下等待电流稳定后记录数值</li>
                                <li>记录每个电压对应的发射电流</li>
                            </ul>
                        </div>

                        <div class="step-card">
                            <div class="step-number">4</div>
                            <h4>重复测量</h4>
                            <ul>
                                <li>将灯丝电流调节到下一个值（0.60A），重复步骤2-3</li>
                                <li>依次完成所有5个电流值的测量</li>
                                <li>每个温度下都要测量8个电压点的电流值</li>
                                <li>总共需要记录40个数据点（5×8）</li>
                            </ul>
                        </div>

                        <div class="step-card">
                            <div class="step-number">5</div>
                            <h4>数据记录与处理</h4>
                            <ul>
                                <li>将所有测量数据整理成表格形式</li>
                                <li>检查数据的合理性，剔除明显的异常值</li>
                                <li>使用本系统进行数据处理和分析</li>
                                <li>计算电子逸出功并分析结果</li>
                            </ul>
                        </div>

                        <!-- 数据处理说明 -->
                        <div class="step-card">
                            <h3><i class="fas fa-chart-line me-2"></i>数据处理流程</h3>
                            <p>本系统将自动完成以下数据处理步骤：</p>
                            <ol>
                                <li><strong>绘制I-U特性曲线</strong> - 展示不同温度下电流随电压的变化</li>
                                <li><strong>Schottky效应分析</strong> - 分析log(I)与√U的线性关系</li>
                                <li><strong>Richardson拟合</strong> - 通过log(I/T²)与1/T的线性拟合计算逸出功</li>
                                <li><strong>结果展示</strong> - 提供详细的计算结果和图表分析</li>
                            </ol>
                        </div>

                        <div class="warning-box">
                            <h4><i class="fas fa-exclamation-circle me-2"></i>常见问题与解决方案</h4>
                            <ul class="mb-0">
                                <li><strong>电流不稳定</strong> - 检查连接是否牢固，等待更长时间让温度稳定</li>
                                <li><strong>温度偏差较大</strong> - 重新校准温度测量设备，确保测量位置正确</li>
                                <li><strong>数据异常</strong> - 重复测量可疑数据点，排除设备故障</li>
                                <li><strong>拟合效果不好</strong> - 检查数据质量，可能需要增加测量点数</li>
                            </ul>
                        </div>

                        <div class="text-center mt-4">
                            <a href="{{ url_for('theory') }}" class="btn btn-outline-secondary me-3">
                                <i class="fas fa-arrow-left me-2"></i>返回原理
                            </a>
                            <a href="{{ url_for('input_data', step=1) }}" class="btn btn-custom">
                                <i class="fas fa-play me-2"></i>开始数据输入
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
