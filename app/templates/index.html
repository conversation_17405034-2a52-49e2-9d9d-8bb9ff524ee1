<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金属电子逸出功实验数据处理</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 40px;
        }
        .header {
            background: linear-gradient(135deg, #67b26f, #4ca2cd);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .card {
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            border: none;
            margin-bottom: 1.5rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #4ca2cd;
            color: white;
            font-weight: bold;
        }
        .form-control, .form-select {
            border-radius: 0.375rem;
        }
        .btn-primary {
            background-color: #4ca2cd;
            border-color: #4ca2cd;
        }
        .btn-primary:hover {
            background-color: #3a8cbb;
            border-color: #3a8cbb;
        }
        .table-responsive {
            border-radius: 0.375rem;
            overflow: hidden;
        }
        .result-table {
            background-color: white;
        }
        .chart-container {
            background-color: white;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .explanation {
            background-color: #e8f4f8;
            border-left: 4px solid #4ca2cd;
        }
        .data-input-table input {
            width: 100px;
            text-align: center;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-size: 0.9rem;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 0.25rem;
        }
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }
        /* 优化表格样式 */
        .table>thead>tr>th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .table-hover tbody tr:hover {
            background-color: rgba(76, 162, 205, 0.1);
        }
        /* 优化表单输入框 */
        .data-input-table input:focus {
            border-color: #4ca2cd;
            box-shadow: 0 0 0 0.2rem rgba(76, 162, 205, 0.25);
        }
    </style>
</head>
<body>
    <div class="container fade-in">
        <!-- Header -->
        <div class="header text-center">
            <h1>金属电子逸出功实验数据处理</h1>
            <p class="lead">基于Richardson-Dushman方程的电子逸出功计算</p>
        </div>

        <!-- Explanation Card -->
        <div class="card explanation">
            <div class="card-body">
                <h5 class="card-title">实验说明</h5>
                <p class="card-text">
                    本程序用于处理金属电子逸出功实验数据。实验中，灯丝电流分别为0.55、0.60、0.65、0.70、0.75安培，
                    对应温度分别为1800K、1880K、1960K、2040K、2120K。阳极电压从16V到121V（16、25、36、49、64、81、100、121）。
                </p>
                <p class="card-text">
                    请在下方表格中输入测量到的40个电流数据（5个温度 × 8个电压）。
                </p>
            </div>
        </div>

        <!-- Data Input Form -->
        <div class="card">
            <div class="card-header">
                测量电流值表格（A）
            </div>
            <div class="card-body">
                <form method="post">
                    <div class="table-responsive data-input-table">
                        <table class="table table-bordered table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>灯丝电流/温度</th>
                                    <th>16V</th>
                                    <th>25V</th>
                                    <th>36V</th>
                                    <th>49V</th>
                                    <th>64V</th>
                                    <th>81V</th>
                                    <th>100V</th>
                                    <th>121V</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>0.55A / 1800K</td>
                                    {% for i in range(8) %}
                                        <td><input type="text" class="form-control form-control-sm" name="current_0_{{ i }}" placeholder="电流值"></td>
                                    {% endfor %}
                                </tr>
                                <tr>
                                    <td>0.60A / 1880K</td>
                                    {% for i in range(8) %}
                                        <td><input type="text" class="form-control form-control-sm" name="current_1_{{ i }}" placeholder="电流值"></td>
                                    {% endfor %}
                                </tr>
                                <tr>
                                    <td>0.65A / 1960K</td>
                                    {% for i in range(8) %}
                                        <td><input type="text" class="form-control form-control-sm" name="current_2_{{ i }}" placeholder="电流值"></td>
                                    {% endfor %}
                                </tr>
                                <tr>
                                    <td>0.70A / 2040K</td>
                                    {% for i in range(8) %}
                                        <td><input type="text" class="form-control form-control-sm" name="current_3_{{ i }}" placeholder="电流值"></td>
                                    {% endfor %}
                                </tr>
                                <tr>
                                    <td>0.75A / 2120K</td>
                                    {% for i in range(8) %}
                                        <td><input type="text" class="form-control form-control-sm" name="current_4_{{ i }}" placeholder="电流值"></td>
                                    {% endfor %}
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">计算电子逸出功</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results Section -->
        {% if results %}
        <div class="results">
            <!-- I-U Characteristic Curve -->
            <div class="card">
                <div class="card-header">
                    I-U特性曲线
                </div>
                <div class="card-body chart-container">
                    <img src="data:image/png;base64,{{ results.iu_plot }}" class="img-fluid" alt="I-U特性曲线">
                </div>
            </div>

            <!-- LogI vs sqrt(U) Linear Fit -->
            <div class="card">
                <div class="card-header">
                    logI与sqrt(U)的线性拟合（不同温度）
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover result-table">
                            <thead class="table-light">
                                <tr>
                                    <th>温度 (K)</th>
                                    <th>拟合方程</th>
                                    <th>R²值</th>
                                    <th>截距 lg(I₀)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in results.logu_fits %}
                                <tr>
                                    <td>{{ item.temp }}</td>
                                    <td>y = {{ item.slope }}x + {{ item.intercept }}</td>
                                    <td>{{ item.r_squared }}</td>
                                    <td>{{ item.intercept }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="chart-container">
                        <img src="data:image/png;base64,{{ results.logu_plot }}" class="img-fluid" alt="logI与sqrt(U)的线性拟合">
                    </div>
                </div>
            </div>

            <!-- lg(I/T²) vs 1/T Linear Fit -->
            <div class="card">
                <div class="card-header">
                    lg(I/T²)与1/T的线性拟合
                </div>
                <div class="card-body">
                    <p>拟合方程: y = {{ results.slope2 }}x + {{ results.intercept2 }}</p>
                    <p>R²: {{ results.r_squared2 }}</p>
                    <div class="chart-container">
                        <img src="data:image/png;base64,{{ results.temp_plot }}" class="img-fluid" alt="lg(I/T²)与1/T的线性拟合">
                    </div>
                </div>
            </div>

            <!-- Work Function Calculation -->
            <div class="card highlight">
                <div class="card-header">
                    电子逸出功计算结果
                </div>
                <div class="card-body">
                    <p class="lead">根据Richardson-Dushman方程: j = AT²exp(-φ/kT)</p>
                    <p>取对数: lg(I/T²) = lg(A) - φ/(k·T·ln(10))</p>
                    <p>斜率与逸出功关系: 斜率 = -φ/(k·ln(10))</p>
                    <h3 class="text-center"><strong>电子逸出功 φ = -k·ln(10)·斜率 = {{ results.work_function }} eV</strong></h3>
                </div>
            </div>

            <!-- Detailed Data -->
            <div class="card">
                <div class="card-header">
                    详细数据
                </div>
                <div class="card-body">
                    <h5 class="card-title">1. 各温度下的lg(I/T²)和1/T值</h5>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover result-table">
                            <thead class="table-light">
                                <tr>
                                    <th>温度 (K)</th>
                                    <th>1/T (K⁻¹)</th>
                                    <th>电流 I (A)</th>
                                    <th>lg(I/T²)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in results.temp_data %}
                                <tr>
                                    <td>{{ item.temp }}</td>
                                    <td>{{ item.inv_temp }}</td>
                                    <td>{{ item.current }}</td>
                                    <td>{{ item.log_i_t2 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <h5 class="card-title mt-4">2. 原始实验数据</h5>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover result-table">
                            <thead class="table-light">
                                <tr>
                                    <th>电压 (V)</th>
                                    {% for temp in results.temperatures %}
                                    <th>{{ temp }}K</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for i in range(results.voltage_count) %}
                                <tr>
                                    <td>{{ results.voltages[i] }}</td>
                                    {% for j in range(results.temp_count) %}
                                    <td>{{ results.currents[j][i] }}</td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Footer -->
        <div class="footer">
            <p>金属电子逸出功实验数据处理系统</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有卡片添加淡入效果
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });
            
            // 为计算按钮添加点击效果
            const calcButton = document.querySelector('button[type="submit"]');
            if (calcButton) {
                calcButton.addEventListener('click', function() {
                    this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 计算中...';
                    this.disabled = true;
                });
            }
        });
    </script>
</body>
</html>
