<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据输入 - 步骤{{ step }} - 金属电子逸出功实验</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 2rem auto;
            padding: 0;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        .content-section {
            padding: 3rem 2rem;
        }
        .input-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
        }
        .progress-container {
            margin: 2rem 0;
        }
        .progress {
            height: 10px;
            border-radius: 10px;
            background-color: #e9ecef;
        }
        .progress-bar {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
        }
        .step-info {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            border-left: 5px solid #00d2d3;
        }
        .data-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .data-table input {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.75rem;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .data-table input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        .data-table input:valid {
            border-color: #28a745;
        }
        .btn-custom {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.6);
            color: white;
        }
        .btn-outline-custom {
            border: 2px solid #667eea;
            color: #667eea;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: transparent;
        }
        .btn-outline-custom:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-3px);
        }
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
            color: #6c757d;
            position: relative;
        }
        .step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }
        .step:last-child::after {
            display: none;
        }
        .temperature-badge {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: #333;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin: 0.5rem;
        }
        .auto-fill-btn {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border: none;
            color: #333;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        .auto-fill-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 234, 167, 0.5);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="fas fa-atom me-2"></i>电子逸出功实验
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">首页</a>
                <a class="nav-link" href="{{ url_for('theory') }}">实验原理</a>
                <a class="nav-link" href="{{ url_for('steps') }}">实验步骤</a>
                <a class="nav-link active" href="#">数据输入</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid" style="margin-top: 76px;">
        <div class="main-container">
            <!-- Hero Section -->
            <div class="hero-section">
                <h1><i class="fas fa-keyboard me-3"></i>数据输入 - 步骤 {{ step }}</h1>
                <p class="lead">灯丝电流: {{ current_current }}A &nbsp;|&nbsp; 温度: {{ current_temp }}K</p>
                <div class="step-indicator">
                    {% for i in range(1, total_steps + 1) %}
                        <div class="step {% if i < step %}completed{% elif i == step %}active{% endif %}">
                            {% if i < step %}
                                <i class="fas fa-check"></i>
                            {% else %}
                                {{ i }}
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <div class="row">
                    <div class="col-lg-10 mx-auto">
                        
                        <!-- Progress Bar -->
                        <div class="progress-container">
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">实验进度</span>
                                <span class="text-muted">{{ step }}/{{ total_steps }} ({{ (step/total_steps*100)|round|int }}%)</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ (step/total_steps*100)|round }}%"
                                     aria-valuenow="{{ step }}" aria-valuemin="0" aria-valuemax="{{ total_steps }}">
                                </div>
                            </div>
                        </div>

                        <!-- Step Information -->
                        <div class="step-info">
                            <h4><i class="fas fa-info-circle me-2"></i>当前测量条件</h4>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="temperature-badge">
                                        <i class="fas fa-thermometer-half me-2"></i>
                                        灯丝电流: {{ current_current }}A
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="temperature-badge">
                                        <i class="fas fa-fire me-2"></i>
                                        灯丝温度: {{ current_temp }}K
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="temperature-badge">
                                        <i class="fas fa-list-ol me-2"></i>
                                        测量点数: 8个电压
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0">
                                请在下表中输入当前温度条件下，不同阳极电压对应的发射电流值。
                                如果某些数据点暂时无法测量，可以留空，系统会自动生成合理的模拟数据。
                            </p>
                        </div>

                        <!-- Data Input Form -->
                        <div class="input-card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4><i class="fas fa-table me-2"></i>电流数据输入</h4>
                                <button type="button" class="auto-fill-btn" onclick="autoFillData()">
                                    <i class="fas fa-magic me-2"></i>自动填充示例数据
                                </button>
                            </div>
                            
                            <form method="post" action="{{ url_for('save_data', step=step) }}" id="dataForm">
                                <div class="data-table">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-dark">
                                            <tr>
                                                <th style="width: 20%;">阳极电压 (V)</th>
                                                <th style="width: 80%;">发射电流 (A)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for voltage in voltages %}
                                            <tr>
                                                <td class="align-middle">
                                                    <strong>{{ voltage }}V</strong>
                                                </td>
                                                <td>
                                                    <input type="number"
                                                           class="form-control"
                                                           name="current_{{ loop.index0 }}"
                                                           placeholder="请输入电流值 (例: 1.23e-6)"
                                                           step="any"
                                                           id="current_{{ loop.index0 }}">
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <div class="d-flex justify-content-between mt-4">
                                    {% if step > 1 %}
                                    <a href="{{ url_for('input_data', step=step-1) }}" class="btn btn-outline-custom">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </a>
                                    {% else %}
                                    <a href="{{ url_for('steps') }}" class="btn btn-outline-custom">
                                        <i class="fas fa-arrow-left me-2"></i>返回步骤
                                    </a>
                                    {% endif %}

                                    <button type="submit" class="btn btn-custom" id="submitBtn">
                                        {% if step < total_steps %}
                                            下一步 <i class="fas fa-arrow-right ms-2"></i>
                                        {% else %}
                                            查看结果 <i class="fas fa-chart-line ms-2"></i>
                                        {% endif %}
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Help Information -->
                        <div class="input-card">
                            <h5><i class="fas fa-question-circle me-2"></i>输入说明</h5>
                            <ul class="mb-0">
                                <li><strong>数据格式</strong>：支持科学计数法，如 1.23e-6 表示 1.23×10⁻⁶</li>
                                <li><strong>单位</strong>：电流单位为安培(A)，通常为微安级别</li>
                                <li><strong>精度</strong>：建议保留3-4位有效数字</li>
                                <li><strong>空值处理</strong>：未填写的数据点将自动生成合理的模拟值</li>
                                <li><strong>数据验证</strong>：系统会自动检查数据的合理性</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 自动填充示例数据
        function autoFillData() {
            const sampleData = [
                1.2e-8, 2.1e-8, 3.5e-8, 5.8e-8, 
                9.2e-8, 1.4e-7, 2.1e-7, 3.1e-7
            ];
            
            for (let i = 0; i < sampleData.length; i++) {
                document.getElementById(`current_${i}`).value = sampleData[i].toExponential(2);
            }
        }

        // 表单提交处理
        document.getElementById('dataForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>处理中...';
            submitBtn.disabled = true;
        });

        // 输入验证
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', function() {
                const value = parseFloat(this.value);
                if (value && (value < 0 || value > 1)) {
                    this.style.borderColor = '#dc3545';
                    this.title = '电流值应该在合理范围内（通常为微安级别）';
                } else {
                    this.style.borderColor = '#28a745';
                    this.title = '';
                }
            });
        });
    </script>
</body>
</html>
