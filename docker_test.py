#!/usr/bin/env python3
"""
Docker容器测试脚本 - 端口20005
"""

import requests
import sys

# 测试数据 - 基于钨丝的典型实验数据
test_data = {
    # 步骤1: 1800K (0.55A)
    1: [1.2e-8, 2.1e-8, 3.5e-8, 5.8e-8, 9.2e-8, 1.4e-7, 2.1e-7, 3.1e-7],
    
    # 步骤2: 1880K (0.60A)  
    2: [2.8e-8, 4.6e-8, 7.2e-8, 1.1e-7, 1.6e-7, 2.3e-7, 3.2e-7, 4.4e-7],
    
    # 步骤3: 1960K (0.65A)
    3: [6.1e-8, 9.8e-8, 1.5e-7, 2.2e-7, 3.1e-7, 4.3e-7, 5.8e-7, 7.6e-7],
    
    # 步骤4: 2040K (0.70A)
    4: [1.3e-7, 2.0e-7, 2.9e-7, 4.1e-7, 5.6e-7, 7.4e-7, 9.6e-7, 1.2e-6],
    
    # 步骤5: 2120K (0.75A)
    5: [2.7e-7, 4.1e-7, 5.8e-7, 7.9e-7, 1.0e-6, 1.3e-6, 1.6e-6, 2.0e-6]
}

def test_docker_application():
    """测试Docker容器中的应用程序"""
    base_url = "http://127.0.0.1:20005"
    
    # 创建session来保持会话
    session = requests.Session()
    
    print("🐳 测试Docker容器中的金属电子逸出功实验应用...")
    print(f"🌐 应用地址: {base_url}")
    
    # 1. 测试首页
    print("\n1. 测试首页...")
    try:
        response = session.get(base_url, timeout=10)
        if response.status_code == 200:
            print("✅ 首页加载成功")
        else:
            print(f"❌ 首页加载失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 首页访问异常: {e}")
        return
    
    # 2. 测试实验原理页面
    print("\n2. 测试实验原理页面...")
    try:
        response = session.get(f"{base_url}/theory", timeout=10)
        if response.status_code == 200:
            print("✅ 实验原理页面加载成功")
        else:
            print(f"❌ 实验原理页面加载失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 实验原理页面访问异常: {e}")
    
    # 3. 测试实验步骤页面
    print("\n3. 测试实验步骤页面...")
    try:
        response = session.get(f"{base_url}/steps", timeout=10)
        if response.status_code == 200:
            print("✅ 实验步骤页面加载成功")
        else:
            print(f"❌ 实验步骤页面加载失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 实验步骤页面访问异常: {e}")
    
    # 4. 测试分步骤数据输入
    print("\n4. 测试分步骤数据输入...")
    
    for step in range(1, 6):
        print(f"\n   步骤 {step}: 输入温度 {1800 + (step-1)*80}K 的数据...")
        
        try:
            # 获取输入页面
            response = session.get(f"{base_url}/input/{step}", timeout=10)
            if response.status_code != 200:
                print(f"❌ 步骤 {step} 输入页面加载失败: {response.status_code}")
                continue
            
            # 准备数据
            data = {}
            for i, current_value in enumerate(test_data[step]):
                data[f'current_{i}'] = str(current_value)
            
            # 提交数据
            response = session.post(f"{base_url}/save_data/{step}", data=data, timeout=10)
            if response.status_code == 302:  # 重定向表示成功
                print(f"✅ 步骤 {step} 数据提交成功")
            else:
                print(f"❌ 步骤 {step} 数据提交失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 步骤 {step} 处理异常: {e}")
    
    # 5. 测试结果页面
    print("\n5. 测试结果页面...")
    try:
        response = session.get(f"{base_url}/results", timeout=15)
        if response.status_code == 200:
            print("✅ 结果页面加载成功")
            print("📊 实验数据处理完成，可以查看计算结果")
        else:
            print(f"❌ 结果页面加载失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 结果页面访问异常: {e}")
    
    print("\n🎉 Docker容器测试完成！")
    print(f"📱 请在浏览器中访问: {base_url}")
    print("🔬 您可以查看完整的实验结果和分析图表")
    print("🐳 应用已在Docker容器中成功运行在端口20005")

if __name__ == "__main__":
    test_docker_application()
