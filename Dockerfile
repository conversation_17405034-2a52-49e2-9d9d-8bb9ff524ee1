FROM gunicorn:v1

# 创建工作目录
WORKDIR /app


# 先安装依赖，有利于缓存
COPY ./app .
COPY requirements.txt .
COPY gunicorn.conf.py .

RUN pip3 install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple   --break-system-packages


# 确保templates目录存在
RUN mkdir -p /app/templates

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1ENV 

# 暴露端口
EXPOSE 7031

# 运行应用
CMD ["gunicorn", "--config", "gunicorn.conf.py", "wsgi:app"]

